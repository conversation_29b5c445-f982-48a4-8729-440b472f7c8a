import React from 'react';
import { Text, TextStyle, StyleSheet } from 'react-native';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';

interface TypographyProps {
  children: React.ReactNode;
  variant?: 'h1' | 'h2' | 'h3' | 'h4' | 'body1' | 'body2' | 'caption' | 'overline';
  color?: 'primary' | 'secondary' | 'muted' | 'inverse' | 'success' | 'warning' | 'error';
  align?: 'left' | 'center' | 'right';
  weight?: 'normal' | 'medium' | 'semibold' | 'bold';
  style?: TextStyle;
  numberOfLines?: number;
}

export const Typography: React.FC<TypographyProps> = ({
  children,
  variant = 'body1',
  color = 'primary',
  align = 'left',
  weight = 'normal',
  style,
  numberOfLines,
}) => {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];

  const getVariantStyle = (): TextStyle => {
    const variants: Record<string, TextStyle> = {
      h1: {
        fontSize: 32,
        lineHeight: 40,
        fontWeight: 'bold',
      },
      h2: {
        fontSize: 28,
        lineHeight: 36,
        fontWeight: 'bold',
      },
      h3: {
        fontSize: 24,
        lineHeight: 32,
        fontWeight: '600',
      },
      h4: {
        fontSize: 20,
        lineHeight: 28,
        fontWeight: '600',
      },
      body1: {
        fontSize: 16,
        lineHeight: 24,
        fontWeight: 'normal',
      },
      body2: {
        fontSize: 14,
        lineHeight: 20,
        fontWeight: 'normal',
      },
      caption: {
        fontSize: 12,
        lineHeight: 16,
        fontWeight: 'normal',
      },
      overline: {
        fontSize: 10,
        lineHeight: 16,
        fontWeight: '500',
        textTransform: 'uppercase',
        letterSpacing: 1.5,
      },
    };

    return variants[variant];
  };

  const getColorStyle = (): TextStyle => {
    const colorMap: Record<string, string> = {
      primary: colors.text,
      secondary: colors.textSecondary,
      muted: colors.textMuted,
      inverse: colors.textInverse,
      success: colors.success,
      warning: colors.warning,
      error: colors.error,
    };

    return { color: colorMap[color] };
  };

  const getWeightStyle = (): TextStyle => {
    const weights: Record<string, TextStyle> = {
      normal: { fontWeight: 'normal' },
      medium: { fontWeight: '500' },
      semibold: { fontWeight: '600' },
      bold: { fontWeight: 'bold' },
    };

    return weights[weight];
  };

  const getAlignStyle = (): TextStyle => {
    return { textAlign: align };
  };

  return (
    <Text
      style={[
        getVariantStyle(),
        getColorStyle(),
        getWeightStyle(),
        getAlignStyle(),
        style,
      ]}
      numberOfLines={numberOfLines}
    >
      {children}
    </Text>
  );
};

// Specialized Typography Components
export const Heading1: React.FC<Omit<TypographyProps, 'variant'>> = (props) => (
  <Typography variant="h1" {...props} />
);

export const Heading2: React.FC<Omit<TypographyProps, 'variant'>> = (props) => (
  <Typography variant="h2" {...props} />
);

export const Heading3: React.FC<Omit<TypographyProps, 'variant'>> = (props) => (
  <Typography variant="h3" {...props} />
);

export const Heading4: React.FC<Omit<TypographyProps, 'variant'>> = (props) => (
  <Typography variant="h4" {...props} />
);

export const BodyText: React.FC<Omit<TypographyProps, 'variant'>> = (props) => (
  <Typography variant="body1" {...props} />
);

export const SmallText: React.FC<Omit<TypographyProps, 'variant'>> = (props) => (
  <Typography variant="body2" {...props} />
);

export const Caption: React.FC<Omit<TypographyProps, 'variant'>> = (props) => (
  <Typography variant="caption" {...props} />
);

export const Overline: React.FC<Omit<TypographyProps, 'variant'>> = (props) => (
  <Typography variant="overline" {...props} />
);

const styles = StyleSheet.create({
  // Add any additional styles here if needed
});
